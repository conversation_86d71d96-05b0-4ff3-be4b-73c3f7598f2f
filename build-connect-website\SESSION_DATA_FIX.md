# Session Data Issue Fix

## Problem Identified

The session data was incomplete and missing important user fields that are frequently accessed throughout the application. The backend user data contains fields like:

```json
{
  "_id": "688f98560d53df7d68d5c101",
  "name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "phone": "8362972782",
  "role": "broker",
  "location": [],
  "isAvailable": true,
  "isEmailVerified": false,
  "isPhoneVerified": false,
  "partnershipRequest": "NONE",
  "createdAt": "2025-08-03T17:11:50.977+00:00",
  "updatedAt": "2025-08-03T17:11:50.977+00:00"
}
```

However, the NextAuth session was only storing minimal fields:
- `id`
- `email`
- `name`
- `role`
- `isVerified`
- `accessToken`

## Root Causes

1. **Incomplete Session Type Definitions**: The NextAuth session and JWT interfaces were missing important user fields.

2. **Incomplete User Object Creation**: The authentication flow wasn't mapping all user fields from the backend response to the session.

3. **Missing JWT/Session Callbacks**: The callbacks weren't handling all the additional user fields.

4. **Incorrect Profile Endpoint**: The profile fetch URL was incorrect (`/profile` instead of `/user/profile`).

## Solution Implemented

### 1. Updated Session Type Definitions

Extended the NextAuth session and JWT interfaces to include all necessary user fields:

```typescript
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      phone?: string;
      role: UserRole;
      isVerified: boolean;
      isEmailVerified: boolean;
      isPhoneVerified: boolean;
      location?: string[];
      isAvailable?: boolean;
      partnershipRequest: 'NONE' | 'Broker' | 'Contractor';
      accessToken: string;
      avatar?: string;
      createdAt?: string;
      updatedAt?: string;
    };
  }
}
```

### 2. Enhanced User Object Creation

Updated the authentication flow to map all user fields from the backend response:

```typescript
return {
  id: userData._id || userData.id || data.sessionId || 'temp-id',
  email: userData.email || credentials.email,
  name: userData.name || userData.username || credentials.email.split('@')[0],
  phone: userData.phone || undefined,
  role: userData.role || 'user' as UserRole,
  isVerified: userData.isEmailVerified || userData.isVerified || false,
  isEmailVerified: userData.isEmailVerified || false,
  isPhoneVerified: userData.isPhoneVerified || false,
  location: userData.location || [],
  isAvailable: userData.isAvailable || true,
  partnershipRequest: userData.partnershipRequest || 'NONE',
  accessToken: data.accessToken,
  avatar: userData.avatar || undefined,
  createdAt: userData.createdAt || undefined,
  updatedAt: userData.updatedAt || undefined,
};
```

### 3. Updated JWT and Session Callbacks

Enhanced the callbacks to handle all user fields:

```typescript
callbacks: {
  async jwt({ token, user }) {
    if (user) {
      token.id = user.id;
      token.role = user.role;
      token.isVerified = user.isVerified;
      token.isEmailVerified = user.isEmailVerified;
      token.isPhoneVerified = user.isPhoneVerified;
      token.phone = user.phone;
      token.location = user.location;
      token.isAvailable = user.isAvailable;
      token.partnershipRequest = user.partnershipRequest;
      token.accessToken = user.accessToken;
      token.avatar = user.avatar;
      token.createdAt = user.createdAt;
      token.updatedAt = user.updatedAt;
    }
    return token;
  },
  async session({ session, token }) {
    if (token) {
      session.user.id = token.id;
      session.user.role = token.role;
      session.user.isVerified = token.isVerified;
      session.user.isEmailVerified = token.isEmailVerified;
      session.user.isPhoneVerified = token.isPhoneVerified;
      session.user.phone = token.phone;
      session.user.location = token.location;
      session.user.isAvailable = token.isAvailable;
      session.user.partnershipRequest = token.partnershipRequest;
      session.user.accessToken = token.accessToken;
      session.user.avatar = token.avatar;
      session.user.createdAt = token.createdAt;
      session.user.updatedAt = token.updatedAt;
    }
    return session;
  },
}
```

### 4. Fixed Profile Endpoint URL

Corrected the profile fetch URL from `/user-service/api/v1/profile` to `/user-service/api/v1/user/profile`.

### 5. Enhanced Response Handling

Added proper handling for wrapped API responses:

```typescript
const response = await userResponse.json();
const userData = response.data || response; // Handle both wrapped and unwrapped responses
```

## Testing

Created a debug component (`SessionDebugger`) and page (`/debug/session`) to verify that all session data is properly populated:

- **Basic User Information**: name, email, role, phone
- **Verification Status**: isEmailVerified, isPhoneVerified, isVerified
- **Additional Information**: location, isAvailable, partnershipRequest, avatar
- **Timestamps**: createdAt, updatedAt
- **Raw Session Data**: Complete JSON dump for debugging

## Benefits

1. **Complete User Context**: All user information is now available in the session without additional API calls.

2. **Better User Experience**: Components can access user phone, location, verification status, etc., directly from the session.

3. **Reduced API Calls**: No need to fetch user profile data separately in components.

4. **Consistent Data**: Session data now matches the backend user model exactly.

5. **Better Role-Based Features**: Components can check verification status, partnership requests, etc., for conditional rendering.

## Usage

After login, components can now access all user data through the session:

```typescript
const { data: session } = useSession();
const user = session?.user;

// Now available:
console.log(user?.phone); // "8362972782"
console.log(user?.isEmailVerified); // false
console.log(user?.isPhoneVerified); // false
console.log(user?.partnershipRequest); // "NONE"
console.log(user?.location); // []
console.log(user?.isAvailable); // true
```

## Next Steps

1. Test the session data with actual login flow
2. Update components that need access to the new session fields
3. Remove any redundant API calls for user profile data
4. Consider adding session refresh mechanism for updated user data
