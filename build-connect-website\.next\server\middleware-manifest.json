{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9f9cf45e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_88e3134f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "O/DBlDC4Rav96soFlTYQ9zOocINeKU1oAcswbi9XtzQ=", "__NEXT_PREVIEW_MODE_ID": "e556aef273e0d25b647cb5fbee332a31", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a33b4cce4a3256f98928a6567d293552005254a04ae0aa5b15515509fc8a449e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "51980a5489c14942ea2a61f57ba0f303d3e81062648871dd88957767e3926b28"}}}, "sortedMiddleware": ["/"], "functions": {}}