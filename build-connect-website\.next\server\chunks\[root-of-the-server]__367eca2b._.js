module.exports = {

"[project]/.next-internal/server/app/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "authOptions": ()=>authOptions,
    "hasRole": ()=>hasRole,
    "isAdmin": ()=>isAdmin,
    "isBroker": ()=>isBroker,
    "isBuyer": ()=>isBuyer,
    "isContractor": ()=>isContractor
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }
                try {
                    // Call the backend API to authenticate user
                    const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:8080")}/user-service/api/v1/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email: credentials.email,
                            password: credentials.password
                        })
                    });
                    console.log('Login response:', response);
                    if (!response.ok) {
                        console.error('Login failed:', response.status, response.statusText);
                        return null;
                    }
                    const data = await response.json();
                    // Check if login was successful and we have an access token
                    if (data && data.accessToken) {
                        try {
                            // Fetch user profile data using the access token
                            const userResponse = await fetch(`${("TURBOPACK compile-time value", "http://localhost:8080")}/user-service/api/v1/user/profile`, {
                                method: 'GET',
                                headers: {
                                    'Authorization': `Bearer ${data.accessToken}`,
                                    'Content-Type': 'application/json'
                                }
                            });
                            if (userResponse.ok) {
                                const response = await userResponse.json();
                                const userData = response.data || response; // Handle both wrapped and unwrapped responses
                                return {
                                    id: userData._id || userData.id || data.sessionId || 'temp-id',
                                    email: userData.email || credentials.email,
                                    name: userData.name || userData.username || credentials.email.split('@')[0],
                                    phone: userData.phone || undefined,
                                    role: userData.role || 'user',
                                    isVerified: userData.isEmailVerified || userData.isVerified || false,
                                    isEmailVerified: userData.isEmailVerified || false,
                                    isPhoneVerified: userData.isPhoneVerified || false,
                                    location: userData.location || [],
                                    isAvailable: userData.isAvailable || true,
                                    partnershipRequest: userData.partnershipRequest || 'NONE',
                                    accessToken: data.accessToken,
                                    avatar: userData.avatar || undefined,
                                    createdAt: userData.createdAt || undefined,
                                    updatedAt: userData.updatedAt || undefined
                                };
                            } else {
                                // If profile fetch fails, create minimal user object
                                console.warn('Failed to fetch user profile, using minimal user data');
                                return {
                                    id: data.sessionId || 'temp-id',
                                    email: credentials.email,
                                    name: credentials.email.split('@')[0],
                                    role: 'user',
                                    isVerified: false,
                                    isEmailVerified: false,
                                    isPhoneVerified: false,
                                    location: [],
                                    isAvailable: true,
                                    partnershipRequest: 'NONE',
                                    accessToken: data.accessToken
                                };
                            }
                        } catch (profileError) {
                            console.error('Error fetching user profile:', profileError);
                            // Fallback to minimal user object
                            return {
                                id: data.sessionId || 'temp-id',
                                email: credentials.email,
                                name: credentials.email.split('@')[0],
                                role: 'user',
                                isVerified: false,
                                isEmailVerified: false,
                                isPhoneVerified: false,
                                location: [],
                                isAvailable: true,
                                partnershipRequest: 'NONE',
                                accessToken: data.accessToken
                            };
                        }
                    }
                    return null;
                } catch (error) {
                    console.error('Authentication error:', error);
                    return null;
                }
            }
        })
    ],
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60
    },
    jwt: {
        maxAge: 30 * 24 * 60 * 60
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.id = user.id;
                token.role = user.role;
                token.isVerified = user.isVerified;
                token.isEmailVerified = user.isEmailVerified;
                token.isPhoneVerified = user.isPhoneVerified;
                token.phone = user.phone;
                token.location = user.location;
                token.isAvailable = user.isAvailable;
                token.partnershipRequest = user.partnershipRequest;
                token.accessToken = user.accessToken;
                token.avatar = user.avatar;
                token.createdAt = user.createdAt;
                token.updatedAt = user.updatedAt;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.id;
                session.user.role = token.role;
                session.user.isVerified = token.isVerified;
                session.user.isEmailVerified = token.isEmailVerified;
                session.user.isPhoneVerified = token.isPhoneVerified;
                session.user.phone = token.phone;
                session.user.location = token.location;
                session.user.isAvailable = token.isAvailable;
                session.user.partnershipRequest = token.partnershipRequest;
                session.user.accessToken = token.accessToken;
                session.user.avatar = token.avatar;
                session.user.createdAt = token.createdAt;
                session.user.updatedAt = token.updatedAt;
            }
            return session;
        },
        async redirect ({ url, baseUrl }) {
            // Handle role-based redirects after login
            if (url.startsWith('/')) return `${baseUrl}${url}`;
            if (new URL(url).origin === baseUrl) return url;
            return baseUrl;
        }
    },
    pages: {
        signIn: '/auth/login',
        error: '/auth/error'
    },
    secret: process.env.NEXTAUTH_SECRET
};
const hasRole = (userRole, allowedRoles)=>{
    return allowedRoles.includes(userRole);
};
const isAdmin = (userRole)=>{
    return userRole === 'admin';
};
const isBroker = (userRole)=>{
    return userRole === 'broker';
};
const isContractor = (userRole)=>{
    return userRole === 'contractor';
};
const isBuyer = (userRole)=>{
    return userRole === 'buyer';
};
}),
"[project]/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>handler,
    "POST": ()=>handler
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
const handler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
;
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__367eca2b._.js.map