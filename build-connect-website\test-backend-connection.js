/**
 * Quick Backend Connection Test
 * Tests if the backend is accessible and responds correctly
 */

const fetch = globalThis.fetch || require('node-fetch');

const BACKEND_URL = 'http://localhost:8080';

async function testBackendConnection() {
  console.log('🔍 Testing Backend Connection...');
  console.log('Backend URL:', BACKEND_URL);

  try {
    // Test 1: Check if backend is accessible
    console.log('\n1. Testing backend accessibility...');
    const healthResponse = await fetch(`${BACKEND_URL}/health`, {
      method: 'GET',
      timeout: 5000,
    });

    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('✅ Backend is accessible');
      console.log('Health response:', healthData);
    } else {
      console.log('❌ Backend health check failed:', healthResponse.status);
    }
  } catch (error) {
    console.log('❌ Backend is not accessible:', error.message);
    console.log('💡 Make sure your backend is running on port 8080');
    return;
  }

  try {
    // Test 2: Check user service
    console.log('\n2. Testing user service...');
    const userServiceResponse = await fetch(
      `${BACKEND_URL}/user-service/health`,
      {
        method: 'GET',
        timeout: 5000,
      }
    );

    if (userServiceResponse.ok) {
      console.log('✅ User service is accessible');
    } else {
      console.log(
        '❌ User service not accessible:',
        userServiceResponse.status
      );
    }
  } catch (error) {
    console.log('❌ User service error:', error.message);
  }

  try {
    // Test 3: Test login endpoint with the credentials that failed
    console.log('\n3. Testing login endpoint...');
    const loginResponse = await fetch(
      `${BACKEND_URL}/user-service/api/v1/login`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'User@123',
        }),
        timeout: 10000,
      }
    );

    console.log('Login response status:', loginResponse.status);

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('✅ Login successful');
      console.log('Login response:', JSON.stringify(loginData, null, 2));
    } else {
      const errorData = await loginResponse.text();
      console.log('❌ Login failed');
      console.log('Error response:', errorData);

      if (loginResponse.status === 401) {
        console.log(
          "💡 This suggests the user credentials are invalid or the user doesn't exist"
        );
      } else if (loginResponse.status === 404) {
        console.log("💡 This suggests the login endpoint doesn't exist");
      } else if (loginResponse.status === 500) {
        console.log('💡 This suggests a server error in the backend');
      }
    }
  } catch (error) {
    console.log('❌ Login endpoint error:', error.message);
  }

  try {
    // Test 4: Test profile endpoint with the access token
    console.log('\n4. Testing profile endpoint...');

    // First, get a fresh access token
    const loginResponse = await fetch(
      `${BACKEND_URL}/user-service/api/v1/login`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'User@123',
        }),
      }
    );

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      const accessToken = loginData.accessToken;

      // Test different authentication methods and endpoints
      const testConfigs = [
        {
          endpoint: '/user-service/api/v1/user/profile',
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
        {
          endpoint: '/user-service/api/v1/profile',
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
        {
          endpoint: '/user-service/api/v1/me',
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
        {
          endpoint: '/user-service/api/v1/user/profile',
          headers: {
            'x-access-token': accessToken,
            'Content-Type': 'application/json',
          },
        },
        {
          endpoint: '/user-service/api/v1/profile',
          headers: {
            'x-access-token': accessToken,
            'Content-Type': 'application/json',
          },
        },
      ];

      for (const config of testConfigs) {
        console.log(
          `Testing: ${config.endpoint} with headers:`,
          Object.keys(config.headers)
        );

        const profileResponse = await fetch(
          `${BACKEND_URL}${config.endpoint}`,
          {
            method: 'GET',
            headers: config.headers,
          }
        );

        console.log(`Response status: ${profileResponse.status}`);

        if (profileResponse.ok) {
          const profileData = await profileResponse.json();
          console.log('✅ Profile fetch successful!');
          console.log('Profile data:', JSON.stringify(profileData, null, 2));
          console.log(
            '🎯 Use this endpoint and header format in your auth config'
          );
          break;
        } else if (profileResponse.status !== 404) {
          const errorData = await profileResponse.text();
          console.log('Error response:', errorData.substring(0, 200));
        }
      }
    }
  } catch (error) {
    console.log('Error testing profile endpoint:', error.message);
  }
}

// Run the test
testBackendConnection().catch(console.error);
