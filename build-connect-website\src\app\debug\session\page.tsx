'use client';

import { DashboardLayout } from '@/components/layout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SessionDebugger } from '@/components/debug/SessionDebugger';

export default function SessionDebugPage() {
  const breadcrumbs = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Debug', href: '/debug' },
    { label: 'Session', current: true },
  ];

  return (
    <ProtectedRoute>
      <DashboardLayout
        breadcrumbs={breadcrumbs}
        title="Session Debug"
        description="Debug session data and user information"
      >
        <SessionDebugger />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
