{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Build-connect%28frontend%29/build-connect/build-connect-website/src/lib/auth.ts"], "sourcesContent": ["import axios from 'axios';\nimport { getSession } from 'next-auth/react';\n\nexport const publicAPIClient = axios.create({\n  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,\n});\n\nexport const privateAPIClient = axios.create({\n  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,\n});\n\nprivateAPIClient.interceptors.request.use(async (config) => {\n  const session = await getSession(); // grabs session from NextAuth\n\n  if (session?.user?.accessToken) {\n    config.headers.Authorization = `Bearer ${session.user.accessToken}`;\n  }\n\n  if (session?.user?.id) {\n    config.headers['X-User-ID'] = session.user.id; // optional\n  }\n\n  return config;\n});\n\n// Optional: handle 401 if you want a fallback like logout\nprivateAPIClient.interceptors.response.use(\n  (res) => res,\n  async (error) => {\n    if (error.response?.status === 401) {\n      // Optional: log user out or redirect to login\n      console.warn('Unauthorized request, redirecting to login...');\n    }\n    return Promise.reject(error);\n  }\n);\n\npublicAPIClient.interceptors.request.use((config) => config);\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,kBAAkB,uIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC1C,OAAO;AACT;AAEO,MAAM,mBAAmB,uIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3C,OAAO;AACT;AAEA,iBAAiB,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO;IAC/C,MAAM,UAAU,MAAM,CAAA,GAAA,gJAAA,CAAA,aAAU,AAAD,KAAK,8BAA8B;IAElE,IAAI,SAAS,MAAM,aAAa;QAC9B,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,WAAW,EAAE;IACrE;IAEA,IAAI,SAAS,MAAM,IAAI;QACrB,OAAO,OAAO,CAAC,YAAY,GAAG,QAAQ,IAAI,CAAC,EAAE,EAAE,WAAW;IAC5D;IAEA,OAAO;AACT;AAEA,0DAA0D;AAC1D,iBAAiB,YAAY,CAAC,QAAQ,CAAC,GAAG,CACxC,CAAC,MAAQ,KACT,OAAO;IACL,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,8CAA8C;QAC9C,QAAQ,IAAI,CAAC;IACf;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,gBAAgB,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAW", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Build-connect%28frontend%29/build-connect/build-connect-website/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport { authOptions } from '@/lib/auth';\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}