{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Build-connect%28frontend%29/build-connect/build-connect-website/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { JWT } from 'next-auth/jwt';\nimport { User, UserRole } from '@/types';\n\n// Extend the built-in session types\ndeclare module 'next-auth' {\n  interface Session {\n    user: {\n      id: string;\n      email: string;\n      name: string;\n      phone?: string;\n      role: UserRole;\n      isVerified: boolean;\n      isEmailVerified: boolean;\n      isPhoneVerified: boolean;\n      location?: string[];\n      isAvailable?: boolean;\n      partnershipRequest: 'NONE' | 'Broker' | 'Contractor';\n      accessToken: string;\n      avatar?: string;\n      createdAt?: string;\n      updatedAt?: string;\n    };\n  }\n\n  interface User {\n    id: string;\n    email: string;\n    name: string;\n    phone?: string;\n    role: UserRole;\n    isVerified: boolean;\n    isEmailVerified: boolean;\n    isPhoneVerified: boolean;\n    location?: string[];\n    isAvailable?: boolean;\n    partnershipRequest: 'NONE' | 'Broker' | 'Contractor';\n    accessToken: string;\n    avatar?: string;\n    createdAt?: string;\n    updatedAt?: string;\n  }\n}\n\ndeclare module 'next-auth/jwt' {\n  interface JWT {\n    id: string;\n    role: UserRole;\n    isVerified: boolean;\n    isEmailVerified: boolean;\n    isPhoneVerified: boolean;\n    phone?: string;\n    location?: string[];\n    isAvailable?: boolean;\n    partnershipRequest: 'NONE' | 'Broker' | 'Contractor';\n    accessToken: string;\n    avatar?: string;\n    createdAt?: string;\n    updatedAt?: string;\n  }\n}\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Call the backend API to authenticate user\n          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user-service/api/v1/login`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({\n              email: credentials.email,\n              password: credentials.password,\n            }),\n          });\n\n          if (!response.ok) {\n            console.error('Login failed:', response.status, response.statusText);\n            return null;\n          }\n\n          const data = await response.json();\n\n          // Check if login was successful and we have an access token\n          if (data && data.accessToken) {\n            // Check if user data is included in login response\n            if (data.data && data.data.user) {\n              const userData = data.data.user;\n\n              return {\n                id: userData._id || userData.id || 'temp-id',\n                email: userData.email || credentials.email,\n                name: userData.name || userData.username || userData.displayName || 'Unknown User',\n                phone: userData.phone || undefined,\n                role: userData.role || 'user' as UserRole,\n                isVerified: userData.isEmailVerified || userData.isVerified || false,\n                isEmailVerified: userData.isEmailVerified || false,\n                isPhoneVerified: userData.isPhoneVerified || false,\n                location: userData.location || [],\n                isAvailable: userData.isAvailable !== undefined ? userData.isAvailable : true,\n                partnershipRequest: userData.partnershipRequest || 'NONE',\n                accessToken: data.accessToken,\n                avatar: userData.avatar || undefined,\n                createdAt: userData.createdAt || undefined,\n                updatedAt: userData.updatedAt || undefined,\n              };\n            }\n\n            // Fallback: Fetch user profile data using the access token\n            try {\n              const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user-service/api/v1/user/profile`, {\n                method: 'GET',\n                headers: {\n                  'Authorization': `Bearer ${data.accessToken}`,\n                  'Content-Type': 'application/json',\n                },\n              });\n\n              if (userResponse.ok) {\n                const response = await userResponse.json();\n                const userData = response.data || response;\n\n                return {\n                  id: userData._id || userData.id || data.sessionId,\n                  email: userData.email || credentials.email,\n                  name: userData.name || userData.username || userData.displayName || 'Unknown User',\n                  phone: userData.phone || undefined,\n                  role: userData.role || 'user' as UserRole,\n                  isVerified: userData.isEmailVerified || userData.isVerified || false,\n                  isEmailVerified: userData.isEmailVerified || false,\n                  isPhoneVerified: userData.isPhoneVerified || false,\n                  location: userData.location || [],\n                  isAvailable: userData.isAvailable !== undefined ? userData.isAvailable : true,\n                  partnershipRequest: userData.partnershipRequest || 'NONE',\n                  accessToken: data.accessToken,\n                  avatar: userData.avatar || undefined,\n                  createdAt: userData.createdAt || undefined,\n                  updatedAt: userData.updatedAt || undefined,\n                };\n              } else {\n                throw new Error(`Profile fetch failed with status: ${userResponse.status}`);\n              }\n            } catch (profileError) {\n              console.error('Error fetching user profile:', profileError);\n\n              // Since login was successful but profile fetch failed,\n              // create a minimal user object with the available data\n              return {\n                id: data.sessionId || 'temp-id',\n                email: credentials.email,\n                name: credentials.email.split('@')[0], // Temporary fallback\n                role: 'user' as UserRole,\n                isVerified: false,\n                isEmailVerified: false,\n                isPhoneVerified: false,\n                location: [],\n                isAvailable: true,\n                partnershipRequest: 'NONE',\n                accessToken: data.accessToken,\n              };\n            }\n          }\n\n          return null;\n        } catch (error) {\n          console.error('Authentication error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  jwt: {\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id;\n        token.role = user.role;\n        token.isVerified = user.isVerified;\n        token.isEmailVerified = user.isEmailVerified;\n        token.isPhoneVerified = user.isPhoneVerified;\n        token.phone = user.phone;\n        token.location = user.location;\n        token.isAvailable = user.isAvailable;\n        token.partnershipRequest = user.partnershipRequest;\n        token.accessToken = user.accessToken;\n        token.avatar = user.avatar;\n        token.createdAt = user.createdAt;\n        token.updatedAt = user.updatedAt;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id;\n        session.user.role = token.role;\n        session.user.isVerified = token.isVerified;\n        session.user.isEmailVerified = token.isEmailVerified;\n        session.user.isPhoneVerified = token.isPhoneVerified;\n        session.user.phone = token.phone;\n        session.user.location = token.location;\n        session.user.isAvailable = token.isAvailable;\n        session.user.partnershipRequest = token.partnershipRequest;\n        session.user.accessToken = token.accessToken;\n        session.user.avatar = token.avatar;\n        session.user.createdAt = token.createdAt;\n        session.user.updatedAt = token.updatedAt;\n      }\n      return session;\n    },\n    async redirect({ url, baseUrl }) {\n      // Handle role-based redirects after login\n      if (url.startsWith('/')) return `${baseUrl}${url}`;\n      if (new URL(url).origin === baseUrl) return url;\n      return baseUrl;\n    },\n  },\n  pages: {\n    signIn: '/auth/login',\n    error: '/auth/error',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n\n// Helper functions for role-based access control\nexport const hasRole = (userRole: UserRole, allowedRoles: UserRole[]): boolean => {\n  return allowedRoles.includes(userRole);\n};\n\nexport const isAdmin = (userRole: UserRole): boolean => {\n  return userRole === 'admin';\n};\n\nexport const isBroker = (userRole: UserRole): boolean => {\n  return userRole === 'broker';\n};\n\nexport const isContractor = (userRole: UserRole): boolean => {\n  return userRole === 'contractor';\n};\n\nexport const isBuyer = (userRole: UserRole): boolean => {\n  return userRole === 'user';\n};\n"], "names": [], "mappings": ";;;;;;;;AACA;;AA+DO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,4CAA4C;oBAC5C,MAAM,WAAW,MAAM,MAAM,6DAAwC,0BAA0B,CAAC,EAAE;wBAChG,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB,OAAO,YAAY,KAAK;4BACxB,UAAU,YAAY,QAAQ;wBAChC;oBACF;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,QAAQ,KAAK,CAAC,iBAAiB,SAAS,MAAM,EAAE,SAAS,UAAU;wBACnE,OAAO;oBACT;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,4DAA4D;oBAC5D,IAAI,QAAQ,KAAK,WAAW,EAAE;wBAC5B,mDAAmD;wBACnD,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;4BAC/B,MAAM,WAAW,KAAK,IAAI,CAAC,IAAI;4BAE/B,OAAO;gCACL,IAAI,SAAS,GAAG,IAAI,SAAS,EAAE,IAAI;gCACnC,OAAO,SAAS,KAAK,IAAI,YAAY,KAAK;gCAC1C,MAAM,SAAS,IAAI,IAAI,SAAS,QAAQ,IAAI,SAAS,WAAW,IAAI;gCACpE,OAAO,SAAS,KAAK,IAAI;gCACzB,MAAM,SAAS,IAAI,IAAI;gCACvB,YAAY,SAAS,eAAe,IAAI,SAAS,UAAU,IAAI;gCAC/D,iBAAiB,SAAS,eAAe,IAAI;gCAC7C,iBAAiB,SAAS,eAAe,IAAI;gCAC7C,UAAU,SAAS,QAAQ,IAAI,EAAE;gCACjC,aAAa,SAAS,WAAW,KAAK,YAAY,SAAS,WAAW,GAAG;gCACzE,oBAAoB,SAAS,kBAAkB,IAAI;gCACnD,aAAa,KAAK,WAAW;gCAC7B,QAAQ,SAAS,MAAM,IAAI;gCAC3B,WAAW,SAAS,SAAS,IAAI;gCACjC,WAAW,SAAS,SAAS,IAAI;4BACnC;wBACF;wBAEA,2DAA2D;wBAC3D,IAAI;4BACF,MAAM,eAAe,MAAM,MAAM,6DAAwC,iCAAiC,CAAC,EAAE;gCAC3G,QAAQ;gCACR,SAAS;oCACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,WAAW,EAAE;oCAC7C,gBAAgB;gCAClB;4BACF;4BAEA,IAAI,aAAa,EAAE,EAAE;gCACnB,MAAM,WAAW,MAAM,aAAa,IAAI;gCACxC,MAAM,WAAW,SAAS,IAAI,IAAI;gCAElC,OAAO;oCACL,IAAI,SAAS,GAAG,IAAI,SAAS,EAAE,IAAI,KAAK,SAAS;oCACjD,OAAO,SAAS,KAAK,IAAI,YAAY,KAAK;oCAC1C,MAAM,SAAS,IAAI,IAAI,SAAS,QAAQ,IAAI,SAAS,WAAW,IAAI;oCACpE,OAAO,SAAS,KAAK,IAAI;oCACzB,MAAM,SAAS,IAAI,IAAI;oCACvB,YAAY,SAAS,eAAe,IAAI,SAAS,UAAU,IAAI;oCAC/D,iBAAiB,SAAS,eAAe,IAAI;oCAC7C,iBAAiB,SAAS,eAAe,IAAI;oCAC7C,UAAU,SAAS,QAAQ,IAAI,EAAE;oCACjC,aAAa,SAAS,WAAW,KAAK,YAAY,SAAS,WAAW,GAAG;oCACzE,oBAAoB,SAAS,kBAAkB,IAAI;oCACnD,aAAa,KAAK,WAAW;oCAC7B,QAAQ,SAAS,MAAM,IAAI;oCAC3B,WAAW,SAAS,SAAS,IAAI;oCACjC,WAAW,SAAS,SAAS,IAAI;gCACnC;4BACF,OAAO;gCACL,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,aAAa,MAAM,EAAE;4BAC5E;wBACF,EAAE,OAAO,cAAc;4BACrB,QAAQ,KAAK,CAAC,gCAAgC;4BAE9C,uDAAuD;4BACvD,uDAAuD;4BACvD,OAAO;gCACL,IAAI,KAAK,SAAS,IAAI;gCACtB,OAAO,YAAY,KAAK;gCACxB,MAAM,YAAY,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gCACrC,MAAM;gCACN,YAAY;gCACZ,iBAAiB;gCACjB,iBAAiB;gCACjB,UAAU,EAAE;gCACZ,aAAa;gCACb,oBAAoB;gCACpB,aAAa,KAAK,WAAW;4BAC/B;wBACF;oBACF;oBAEA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yBAAyB;oBACvC,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,KAAK;QACH,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,UAAU,GAAG,KAAK,UAAU;gBAClC,MAAM,eAAe,GAAG,KAAK,eAAe;gBAC5C,MAAM,eAAe,GAAG,KAAK,eAAe;gBAC5C,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,WAAW,GAAG,KAAK,WAAW;gBACpC,MAAM,kBAAkB,GAAG,KAAK,kBAAkB;gBAClD,MAAM,WAAW,GAAG,KAAK,WAAW;gBACpC,MAAM,MAAM,GAAG,KAAK,MAAM;gBAC1B,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,SAAS,GAAG,KAAK,SAAS;YAClC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;gBAC1C,QAAQ,IAAI,CAAC,eAAe,GAAG,MAAM,eAAe;gBACpD,QAAQ,IAAI,CAAC,eAAe,GAAG,MAAM,eAAe;gBACpD,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;gBAC5C,QAAQ,IAAI,CAAC,kBAAkB,GAAG,MAAM,kBAAkB;gBAC1D,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;gBAC5C,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;gBAClC,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;YAC1C;YACA,OAAO;QACT;QACA,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,0CAA0C;YAC1C,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;YAClD,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YAC5C,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC;AAGO,MAAM,UAAU,CAAC,UAAoB;IAC1C,OAAO,aAAa,QAAQ,CAAC;AAC/B;AAEO,MAAM,UAAU,CAAC;IACtB,OAAO,aAAa;AACtB;AAEO,MAAM,WAAW,CAAC;IACvB,OAAO,aAAa;AACtB;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,aAAa;AACtB;AAEO,MAAM,UAAU,CAAC;IACtB,OAAO,aAAa;AACtB", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Build-connect%28frontend%29/build-connect/build-connect-website/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport { authOptions } from '@/lib/auth';\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}