import { NextAuthOptions } from 'next-auth';
import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import { JWT } from 'next-auth/jwt';
import { User, UserRole } from '@/types';

// Extend the built-in session types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: UserRole;
      isVerified: boolean;
      accessToken: string;
    };
  }

  interface User {
    id: string;
    email: string;
    name: string;
    role: UserRole;
    isVerified: boolean;
    accessToken: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: UserRole;
    isVerified: boolean;
    accessToken: string;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Call the backend API to authenticate user
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user-service/api/v1/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          });
          console.log('Login response:', response);

          if (!response.ok) {
            console.error('Login failed:', response.status, response.statusText);
            return null;
          }

          const data = await response.json();

          // Check if login was successful and we have an access token
          if (data && data.accessToken) {
            try {
              // Fetch user profile data using the access token
              const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user-service/api/v1/profile`, {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${data.accessToken}`,
                  'Content-Type': 'application/json',
                },
              });

              if (userResponse.ok) {
                const userData = await userResponse.json();

                return {
                  id: userData._id || userData.id || data.sessionId || 'temp-id',
                  email: userData.email || credentials.email,
                  name: userData.name || userData.username || credentials.email.split('@')[0],
                  role: userData.role || 'user' as UserRole, // Use actual role from backend
                  isVerified: userData.isEmailVerified || userData.isVerified || false,
                  accessToken: data.accessToken,
                };
              } else {
                // If profile fetch fails, create minimal user object
                console.warn('Failed to fetch user profile, using minimal user data');
                return {
                  id: data.sessionId || 'temp-id',
                  email: credentials.email,
                  name: credentials.email.split('@')[0],
                  role: 'user' as UserRole, // Default to user role instead of admin
                  isVerified: false,
                  accessToken: data.accessToken,
                };
              }
            } catch (profileError) {
              console.error('Error fetching user profile:', profileError);
              // Fallback to minimal user object
              return {
                id: data.sessionId || 'temp-id',
                email: credentials.email,
                name: credentials.email.split('@')[0],
                role: 'user' as UserRole,
                isVerified: false,
                accessToken: data.accessToken,
              };
            }
          }

          return null;
        } catch (error) {
          console.error('Authentication error:', error);
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.isVerified = user.isVerified;
        token.accessToken = user.accessToken;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id;
        session.user.role = token.role;
        session.user.isVerified = token.isVerified;
        session.user.accessToken = token.accessToken;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Handle role-based redirects after login
      if (url.startsWith('/')) return `${baseUrl}${url}`;
      if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

// Helper functions for role-based access control
export const hasRole = (userRole: UserRole, allowedRoles: UserRole[]): boolean => {
  return allowedRoles.includes(userRole);
};

export const isAdmin = (userRole: UserRole): boolean => {
  return userRole === 'admin';
};

export const isBroker = (userRole: UserRole): boolean => {
  return userRole === 'broker';
};

export const isContractor = (userRole: UserRole): boolean => {
  return userRole === 'contractor';
};

export const isBuyer = (userRole: UserRole): boolean => {
  return userRole === 'buyer';
};
